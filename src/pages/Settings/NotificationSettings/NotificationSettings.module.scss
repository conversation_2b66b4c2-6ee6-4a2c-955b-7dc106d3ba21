@use '../../../styles/media' as *;

.notificationSettings {
  .innerWrapper {
    background-color: var(--neutral-600);
    border-radius: 8px;
    padding: 0 15px;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-top: 15px;

    @include mobile {
      margin-bottom: 24px;
    }
  }

  .title {
    color: var(--white);
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-semibold);
    margin: 0;
  }

  .settingsList {
    padding-bottom: 20px;
  }

  .settingItem {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 24px 0;
    border-bottom: 1px solid var(--neutral-500);

    &:first-child {
      padding-top: 0;
    }

    &:last-child {
      border-bottom: none;
      padding-bottom: 0;
    }

    @include mobile {
      align-items: center;
      gap: 15px;
      padding: 20px 0;
    }
  }

  .settingContent {
    flex: 1;
    margin-right: 20px;

    @include mobile {
      margin-right: 0;
    }
  }

  .settingTitle {
    color: var(--white);
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-medium);
    margin: 0 0 6px 0;
    line-height: 1.4;
  }

  .settingDescription {
    color: var(--neutral-300);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-normal);
    margin: 0;
    line-height: 1.5;
  }

  .switchWrapper {
    flex-shrink: 0;
    display: flex;
    align-items: center;
  }

  .switch {
    &.ant-switch {
      background-color: var(--neutral-500);
      border: 1px solid var(--neutral-400);

      &:hover:not(.ant-switch-disabled) {
        background-color: var(--neutral-400);
      }

      &.ant-switch-checked {
        background-color: var(--primary-500);
        border-color: var(--primary-500);

        &:hover:not(.ant-switch-disabled) {
          background-color: var(--primary-400);
          border-color: var(--primary-400);
        }
      }

      .ant-switch-handle {
        background-color: var(--white);

        &::before {
          background-color: var(--white);
        }
      }

      &.ant-switch-checked .ant-switch-handle {
        background-color: var(--black);

        &::before {
          background-color: var(--black);
        }
      }
    }
  }
}
