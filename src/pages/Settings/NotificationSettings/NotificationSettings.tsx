import { Switch } from 'antd';
import { useState } from 'react';
import styles from './NotificationSettings.module.scss';

interface NotificationSettingsState {
  emailMarketing: boolean;
  bonusNotifications: boolean;
  winNotifications: boolean;
  depositNotifications: boolean;
  withdrawalNotifications: boolean;
}

export const NotificationSettings = () => {
  const [settings, setSettings] = useState<NotificationSettingsState>({
    emailMarketing: false,
    bonusNotifications: true,
    winNotifications: false,
    depositNotifications: false,
    withdrawalNotifications: false,
  });

  const handleSettingChange = (key: keyof NotificationSettingsState, value: boolean) => {
    setSettings((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const notificationOptions = [
    {
      key: 'emailMarketing' as const,
      title: 'Email marketing',
      description: 'Get updates on promotions and news directly to your email.',
    },
    {
      key: 'bonusNotifications' as const,
      title: 'Bonus notifications',
      description: 'Receive alerts when bonuses are available or about to expire.',
    },
    {
      key: 'winNotifications' as const,
      title: 'Win notifications',
      description: 'Celebrate your victories with instant updates on your wins.',
    },
    {
      key: 'depositNotifications' as const,
      title: 'Deposit notifications',
      description: 'Receive confirmation when your deposit is successful.',
    },
    {
      key: 'withdrawalNotifications' as const,
      title: 'Withdrawal notifications',
      description: 'Get updates when your withdrawal is processed.',
    },
  ];

  return (
    <div className={styles.notificationSettings}>
      <div className={styles.innerWrapper}>
        <div className={styles.header}>
          <h2 className={styles.title}>Notification Settings</h2>
        </div>

        <div className={styles.settingsList}>
          {notificationOptions.map((option) => (
            <div key={option.key} className={styles.settingItem}>
              <div className={styles.settingContent}>
                <h3 className={styles.settingTitle}>{option.title}</h3>
                <p className={styles.settingDescription}>{option.description}</p>
              </div>
              <div className={styles.switchWrapper}>
                <Switch
                  checked={settings[option.key]}
                  onChange={(checked) => handleSettingChange(option.key, checked)}
                  className={styles.switch}
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
