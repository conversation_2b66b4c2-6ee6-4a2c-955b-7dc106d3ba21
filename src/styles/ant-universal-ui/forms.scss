// Universal form styles for Ant Design components
.ant-form-item {
  margin-bottom: 35px;
}

// Universal input styles
.ant-input,
.ant-select-selector {
  background-color: var(--neutral-800);
  border: 1px solid var(--neutral-500);
  color: var(--white);
  border-radius: 6px;

  &::placeholder {
    color: var(--neutral-300);
  }
}

.ant-select {
  height: 40px;
}

.ant-form-item-extra {
  margin-top: 5px;
  color: var(--neutral-400);
  font-size: var(--font-size-xs);
}

.ant-picker input {
  color: var(--white);

  &::placeholder {
    color: var(--neutral-300);
  }
}

.ant-input {
  font-weight: var(--font-weight-normal);
  font-size: var(--font-size-sm);
  line-height: 30px;

  &::placeholder {
    color: var(--neutral-300);
  }
}

.ant-input-password {
  background-color: var(--neutral-800);
  border: 1px solid var(--neutral-500);
  color: var(--white);
  border-radius: 6px;

  .ant-input-password-icon {
    color: var(--neutral-300);

    &:hover {
      color: var(--primary-500);
    }
  }

  .ant-input {
    border: 0;
    background: transparent;
  }
}

// Date picker styles
.ant-picker {
  background-color: var(--neutral-800);
  border: 1px solid var(--neutral-500);
  border-radius: 6px;
  width: 100%;
  line-height: 30px;

  .ant-picker-suffix {
    color: var(--neutral-300);
  }
}

// Select styles
.ant-select {
  .ant-select-selector {
    background-color: var(--neutral-800);
    border: 1px solid var(--neutral-500);
    border-radius: 6px;
  }

  .ant-select-selection-item {
    color: var(--white);
  }

  .ant-select-arrow {
    color: var(--neutral-300);
  }
}

// Focus states (higher specificity than default Ant Design)
.ant-form-item:not(.ant-form-item-has-error) {
  .ant-input:focus,
  .ant-input:focus-within,
  .ant-input-password:focus-within,
  .ant-picker:focus-within,
  .ant-select-focused .ant-select-selector {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 2px rgba(213, 255, 0, 0.2);
    outline: none;
  }
}

// Hover states for non-error fields
.ant-form-item:not(.ant-form-item-has-error) {
  .ant-input:hover,
  .ant-input-password:hover,
  .ant-picker:hover,
  .ant-select:hover .ant-select-selector {
    border-color: var(--primary-500);
  }
}

// Error states
.ant-form-item-has-error {
  .ant-input,
  .ant-input-password,
  .ant-picker,
  .ant-select .ant-select-selector {
    border-color: var(--error);
    box-shadow: 0 0 0 2px rgba(240, 81, 81, 0.2);
  }

  // Focus state for error fields should maintain error color
  .ant-input:focus,
  .ant-input:focus-within,
  .ant-input-password:focus-within,
  .ant-picker:focus-within,
  .ant-select-focused .ant-select-selector {
    border-color: var(--error);
    box-shadow: 0 0 0 2px rgba(240, 81, 81, 0.2);
  }
}

// Form error messages
.ant-form-item-explain-error {
  color: var(--error);
  font-size: var(--font-size-xs);
}

// Checkbox styles
.ant-checkbox {
  .ant-checkbox-inner {
    background-color: var(--neutral-800);
    border: 1px solid var(--neutral-500);
    border-radius: 4px;
  }

  &.ant-checkbox-checked .ant-checkbox-inner {
    background-color: var(--primary-500);
    border-color: var(--primary-500);

    &::after {
      border-color: var(--black);
    }
  }

  &:hover .ant-checkbox-inner {
    border-color: var(--primary-500);
  }
}

.ant-checkbox-wrapper {
  color: var(--white);

  a {
    color: var(--primary-500);
    text-decoration: none;

    &:hover {
      color: var(--primary-500);
      text-decoration: underline;
    }
  }
}

// Error state for checkboxes
.ant-form-item-has-error {
  .ant-checkbox .ant-checkbox-inner {
    border-color: var(--error);
  }

  .ant-checkbox-wrapper {
    color: var(--white);
  }
}

// Switch styles
.ant-switch {
  background-color: var(--neutral-500);
  border: 1px solid var(--neutral-400);

  &:hover:not(.ant-switch-disabled) {
    background-color: var(--neutral-400);
  }

  &.ant-switch-checked {
    background-color: var(--primary-500);
    border-color: var(--primary-500);

    &:hover:not(.ant-switch-disabled) {
      background-color: var(--primary-400);
      border-color: var(--primary-400);
    }
  }

  .ant-switch-handle {
    background-color: var(--white);

    &::before {
      background-color: var(--white);
    }
  }

  &.ant-switch-checked .ant-switch-handle {
    background-color: var(--black);

    &::before {
      background-color: var(--black);
    }
  }

  &:focus {
    box-shadow: 0 0 0 2px rgba(var(--primary-500-rgb), 0.2);
  }
}
